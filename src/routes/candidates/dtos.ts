import {
  CANDIDATE_SOURCES,
  CANDIDATE_STATUSES,
  selectCandidateSchema,
  selectUserSchema,
} from "@/db/schema";
import { createPaginatedSchema, paginationParamsSchema } from "@/lib/schemas";
import { z } from "zod";

export const createCandidateDTO = z.object({
  fullName: z.string().min(1),
  phoneNumbers: z.array(z.string().min(1)),
  age: z.number().int().min(16).max(100).optional(),
  citizenship: z.string().optional(),

  // Professional info
  specialization: z.string().optional(),
  specialtyExperience: z.string().optional(),
  foreignExperience: z.string().optional(),
  totalExperience: z.string().optional(),
  languageSkills: z.string().optional(),
  drivingLicense: z.string().optional(),
  travelDocument: z.string().optional(),

  // Current status
  currentCountry: z.string().optional(),
  currentJob: z.string().optional(),
  interestedCountry: z.string().optional(),
  availabilityDate: z.string().optional(),

  // Application management
  status: z.enum(CANDIDATE_STATUSES).optional(),
  statusComment: z.string().optional(),
  responsibleManagerId: z.string().optional(),
  interestedVacancies: z.array(z.string()).optional(),
  candidateComment: z.string().optional(),
  source: z.enum(CANDIDATE_SOURCES).optional(),
  referral: z.string().optional(),
});

const candidateWithResponsibleManagerDTO = selectCandidateSchema
  .omit({
    responsibleManagerId: true,
  })
  .extend({
    responsibleManager: selectUserSchema.pick({
      id: true,
      name: true,
      image: true,
    }),
  });

export const updateCandidateDTO = createCandidateDTO.partial();

export const candidateWithApplicationsDTO =
  candidateWithResponsibleManagerDTO.extend({
    jobApplications: z.array(
      z.object({
        id: z.string(),
        createdDate: z.string(),
        status: z.enum([
          "not_suitable",
          "no_contact",
          "non_target",
          "duplicate",
          "contact_needed",
          "in_progress",
        ]),
        applicationType: z.enum([
          "application_form",
          "vacancy_response",
          "karta_pobytu",
          "contact_request",
          "question",
          "chat",
          "callback_request",
          "partnership",
        ]),
        vacancyLink: z.string().nullable(),
        createdAt: z.date(),
        jobVacancy: z
          .object({
            id: z.string(),
            slug: z.string(),
            country: z.string(),
          })
          .nullable(),
      }),
    ),
  });

export const mergeCandidatesDTO = z.object({
  targetCandidateId: z.string().min(1),
  sourceCandidateId: z.string().min(1),
});

export const listCandidatesQueryDTO = paginationParamsSchema.extend({
  search: z.string().optional(),
  status: z.enum(CANDIDATE_STATUSES).optional(),
  source: z.enum(CANDIDATE_SOURCES).optional(),
  responsibleManagerId: z.string().optional(),
});

export const listCandidatesDTO = createPaginatedSchema(
  candidateWithResponsibleManagerDTO,
);
