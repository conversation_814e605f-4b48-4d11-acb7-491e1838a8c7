import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import z from "zod";

import { selectUserSchema } from "@/db/schema";
import { auth } from "@/lib/auth";
import { API_TAGS } from "@/lib/constants";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { openApiResponses, responses } from "@/lib/responses";
import { listUsersDTO } from "@/lib/schemas";
import {
  paginationParamsSchema,
  patchUserSchema,
  userFilterSchema,
} from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import {
  changeRoleSchema,
  createUserSchema,
  deleteAccountSchema,
  vacancyAuthorsResponseSchema,
} from "./dtos";
import { UsersOperations } from "./operations";

const router = createRouter()
  .use(authGuard)
  .get(
    "/",
    describeRoute({
      title: "List Users",
      description: "Get a paginated and filterable list of all users.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(listUsersDTO, "List of users"),
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator(
      "query",
      paginationParamsSchema.merge(userFilterSchema).optional(),
    ),
    async (c) => {
      const result = await UsersOperations.findMany(c.req.valid("query") || {});
      return c.json(result, HttpStatusCodes.OK);
    },
  )
  .get(
    "/me",
    describeRoute({
      title: "Get Current User",
      description: "Get the current user's information.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema,
          "Current user information",
        ),
        ...openApiResponses.unauthorized,
      },
    }),
    async (c) => {
      return c.json(c.var.checkedUser, HttpStatusCodes.OK);
    },
  )
  .get(
    "/vacancy-authors",
    describeRoute({
      title: "Get Vacancy Authors",
      description:
        "Get all users who have posted job vacancies (only id, name, and avatar).",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          vacancyAuthorsResponseSchema,
          "List of users who posted vacancies",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ user: ["list"] }),
    async (c) => {
      const authors = await UsersOperations.findVacancyAuthors();
      return c.json(authors, HttpStatusCodes.OK);
    },
  )
  .post(
    "/editors",
    describeRoute({
      title: "Create Editor",
      description:
        "Create a new editor user. Requires editor creation permission.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          selectUserSchema,
          "Created editor user",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ editor: ["create"] }),
    validator("json", createUserSchema),
    async (c) => {
      const data = c.req.valid("json");
      const result = await auth.api.createUser({
        body: {
          ...data,
          role: "editor",
          emailVerified: true,
        },
        headers: c.req.raw.headers,
      });

      return c.json(result.user, HttpStatusCodes.CREATED);
    },
  )
  .patch(
    "/:id/role",
    describeRoute({
      title: "Change User Role",
      description: "Change a user's role. Requires role management permission.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.notFound,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ user: ["set-role"] }),
    validator("json", changeRoleSchema),
    async (c) => {
      const { id } = c.req.param();
      const { role } = c.req.valid("json");

      const result = await auth.api.setRole({
        body: {
          userId: id,
          role,
        },
        headers: c.req.raw.headers,
      });
      if (!result.user) {
        return responses.notFound(c, `User with ID ${id}`);
      }

      return c.json(result.user, HttpStatusCodes.OK);
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get User by ID",
      description: "Get a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema.pick({
            id: true,
            name: true,
            description: true,
            email: true,
            image: true,
            phoneNumber: true,
            dateOfBirth: true,
            gender: true,
            role: true,
            createdAt: true,
            updatedAt: true,
          }),
          "User details",
        ),
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      const { id } = c.req.valid("param");
      const found = await UsersOperations.findById(id);
      if (!found) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.json(found, HttpStatusCodes.OK);
    },
  )
  .patch(
    "/:id",
    describeRoute({
      title: "Update User",
      description: "Update a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ user: ["set-password"] }),
    validator("param", z.object({ id: z.string() })),
    validator("json", patchUserSchema),
    async (c) => {
      const { id } = c.req.valid("param");
      const updates = c.req.valid("json");
      const updated = await UsersOperations.patch(id, updates);
      if (!updated) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.json(updated, HttpStatusCodes.OK);
    },
  )
  .delete(
    "/:id",
    describeRoute({
      title: "Delete User",
      description: "Delete a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: { description: "User deleted" },
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ user: ["delete"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      const { id } = c.req.valid("param");
      const deleted = await UsersOperations.delete(id);
      if (!deleted) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.body(null, HttpStatusCodes.NO_CONTENT);
    },
  )
  .post(
    "/delete-account",
    describeRoute({
      title: "Delete Account",
      description:
        "Delete the current user's account with password verification. This will archive all published vacancies and delete draft vacancies.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.object({
            success: z.boolean(),
            deletionReason: z.string(),
            deletedAt: z.date(),
            message: z.string(),
          }),
          "Account deletion confirmation",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    validator("json", deleteAccountSchema),
    async (c) => {
      const { password, deletionReason } = c.req.valid("json");
      const userId = c.var.checkedUser.id;

      try {
        const result = await UsersOperations.deleteAccount(
          userId,
          password,
          deletionReason,
        );
        return c.json(
          {
            success: result.success,
            deletionReason: result.deletionReason,
            deletedAt: result.deletedAt,
            message:
              "Account has been successfully deleted and data anonymized.",
          },
          HttpStatusCodes.OK,
        );
      } catch (error) {
        return responses.unprocessableEntity(c, (error as Error).message);
      }
    },
  );

export default router;
