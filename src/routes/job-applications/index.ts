import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";

import db from "@/db";
import {
  patchJobApplicationSchema,
  selectJobApplicationSchema,
} from "@/db/schema";
import { API_TAGS } from "@/lib/constants";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { openApiResponses, responses } from "@/lib/responses";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { PublicJobDTO } from "../job-vacancies/dtos";
import { JobVacancyOperations } from "../job-vacancies/operations";
import {
  applicationStatsDTO,
  bulkUpdateApplicationsDTO,
  createJobApplicationDTO,
  jobApplicationWithCandidateDTO,
  listApplicationsDTO,
  listApplicationsQueryDTO,
} from "./dtos";
import { JobApplicationOperations } from "./operations";

const router = createRouter()
  .get(
    "/",
    authGuard,
    describeRoute({
      title: "List Job Applications",
      description: "List all job applications. Requires 'read' permission.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          listApplicationsDTO,
          "List of job applications",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ jobApplication: ["read"] }),
    validator("query", listApplicationsQueryDTO.optional()),
    async (c) => {
      const query = c.req.valid("query");
      const applications = await JobApplicationOperations.findMany(
        c.var.checkedUser.id,
        c.var.checkedUser.role,
        query,
      );
      return c.json(applications, HttpStatusCodes.OK);
    },
  )
  .get(
    "/:id",
    authGuard,
    describeRoute({
      title: "Get Job Application",
      description:
        "Get a specific job application by ID. Requires 'read' permission.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          jobApplicationWithCandidateDTO
            .omit({
              jobVacancyId: true,
            })
            .extend({
              jobVacancy: PublicJobDTO.nullable(),
            }),
          "Single job application with candidate and vacancy details",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ jobApplication: ["read"] }),
    async (c) => {
      const { id } = c.req.param();
      const application =
        await JobApplicationOperations.findByIdWithVacancy(id);

      if (!application) {
        return responses.notFound(c, `Job application with ID ${id}`);
      }

      return c.json(application, HttpStatusCodes.OK);
    },
  )
  .get(
    "/candidate/:candidateId",
    authGuard,
    describeRoute({
      title: "Get Applications by Candidate",
      description: "Get all applications for a specific candidate",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.array(jobApplicationWithCandidateDTO),
          "List of applications for the candidate",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ jobApplication: ["read"] }),
    async (c) => {
      const { candidateId } = c.req.param();
      const applications = await db.query.jobApplication.findMany({
        where: { candidateId },
        with: {
          candidate: true,
          jobVacancy: true,
        },
      });
      return c.json(applications, HttpStatusCodes.OK);
    },
  )
  .get(
    "/stats",
    authGuard,
    describeRoute({
      title: "Get Application Statistics",
      description: "Get statistics about job applications",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          applicationStatsDTO,
          "Application statistics",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ jobApplication: ["stats"] }),
    async (c) => {
      const stats = await JobApplicationOperations.getStats(
        c.var.checkedUser.id,
        c.var.checkedUser.role,
      );
      return c.json(stats, HttpStatusCodes.OK);
    },
  )
  .post(
    "/",
    // TODO fix rate limit
    // rateLimit({
    //   max: 1,
    //   window: "5m",
    // }),
    describeRoute({
      title: "Create Job Application",
      description:
        "Create a new job application. Public endpoint with rate limit of 1 request per 5 minutes.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          jobApplicationWithCandidateDTO,
          "Created job application with candidate data",
        ),
        ...openApiResponses.tooManyRequests,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    validator("json", createJobApplicationDTO),
    async (c) => {
      const data = c.req.valid("json");

      // Validate job vacancy if provided
      if (data.jobVacancyId) {
        const vacancy = await JobVacancyOperations.findById(data.jobVacancyId);
        if (!vacancy) {
          return responses.unprocessableEntity(c, "Invalid job vacancy ID");
        }
      }

      if (data.responsibleManagerId) {
        const manager = await db.query.user.findFirst({
          where: {
            id: data.responsibleManagerId,
          },
        });
        if (!manager) {
          return responses.unprocessableEntity(c, "Invalid manager ID");
        }
      }

      const application =
        await JobApplicationOperations.createWithCandidateData(data);

      return c.json(application, HttpStatusCodes.CREATED);
    },
  )
  .patch(
    "/:id",
    authGuard,
    describeRoute({
      title: "Update Job Application",
      description: "Update a job application. Requires 'update' permission.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectJobApplicationSchema,
          "Updated job application",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ jobApplication: ["update"] }),
    validator("json", patchJobApplicationSchema),
    async (c) => {
      const { id } = c.req.param();
      const updates = c.req.valid("json");

      if (updates.responsibleManagerId) {
        const manager = await db.query.user.findFirst({
          where: {
            id: updates.responsibleManagerId,
          },
        });
        if (!manager) {
          return responses.unprocessableEntity(c, "Invalid manager ID");
        }
      }

      const application = await JobApplicationOperations.update(id, updates);
      if (!application) {
        return responses.notFound(c, `Job application with ID ${id}`);
      }
      return c.json(application, HttpStatusCodes.OK);
    },
  )
  .patch(
    "/bulk",
    authGuard,
    describeRoute({
      title: "Bulk Update Applications",
      description:
        "Update multiple job applications at once. Requires 'bulk_update' permission.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.array(selectJobApplicationSchema),
          "Updated applications",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ jobApplication: ["bulk_update"] }),
    validator("json", bulkUpdateApplicationsDTO),
    async (c) => {
      const { applicationIds, updates } = c.req.valid("json");
      const updatedApplications = await JobApplicationOperations.bulkUpdate(
        applicationIds,
        updates,
      );
      return c.json(updatedApplications, HttpStatusCodes.OK);
    },
  )
  .delete(
    "/:id",
    authGuard,
    describeRoute({
      title: "Delete Job Application",
      description: "Delete a job application. Requires 'delete' permission.",
      tags: [API_TAGS.JOB_APPLICATIONS],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: {
          description: "Job application deleted",
        },
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ jobApplication: ["delete"] }),
    async (c) => {
      const { id } = c.req.param();
      const deleted = await JobApplicationOperations.delete(id);
      if (!deleted) {
        return responses.notFound(c, `Job application with ID ${id}`);
      }
      return c.body(null, HttpStatusCodes.NO_CONTENT);
    },
  );

export default router;
